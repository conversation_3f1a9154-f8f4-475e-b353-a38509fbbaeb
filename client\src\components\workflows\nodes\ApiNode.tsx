import React from 'react';
import { NodeProps } from 'reactflow';
import BaseNode from './BaseNode';
import { Globe, Clock, Shield, AlertCircle } from 'lucide-react';

const ApiNode: React.FC<NodeProps> = ({ id, data, selected }) => {
  const hasSecurityFeatures = data.authType !== 'none' ||
    (data.rateLimit?.enabled) ||
    (data.timeout?.enabled) ||
    (data.requestValidation?.enabled);

  return (
    <BaseNode
      id={id}
      data={data}
      icon={<Globe size={16} />}
      color="#0078D4"
      hasInput={true}
      hasOutput={true}
      selected={selected}
      executionStatus={data.executionStatus}
    >
      <div className="mt-2 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs font-mono overflow-hidden">
        <div className="flex items-center justify-between">
          <span>{data.method || 'POST'}</span>
          <span className="text-green-600 dark:text-green-400">
            {data.authType && data.authType !== 'none' ? `🔒 ${data.authType === 'apiKey' ? 'API Key' : 'Bearer'}` : '🔓 Public'}
          </span>
        </div>
        <div className="mt-1 text-blue-600 dark:text-blue-400 truncate" title={data.url}>
          {data.url || 'No URL configured'}
        </div>

        {hasSecurityFeatures && (
          <div className="mt-2 pt-2 border-t border-neutral-200 dark:border-neutral-700">
            <div className="flex flex-wrap gap-2">
              {data.rateLimit?.enabled && (
                <div className="flex items-center text-amber-600 dark:text-amber-400" title={`Rate limit: ${data.rateLimit.requestsPerMinute} requests/minute`}>
                  <Clock size={12} className="mr-1" />
                  {data.rateLimit.requestsPerMinute}/min
                </div>
              )}
              {data.timeout?.enabled && (
                <div className="flex items-center text-amber-600 dark:text-amber-400" title={`Timeout: ${data.timeout.milliseconds}ms`}>
                  <AlertCircle size={12} className="mr-1" />
                  {data.timeout.milliseconds}ms
                </div>
              )}
              {data.requestValidation?.enabled && (
                <div className="flex items-center text-amber-600 dark:text-amber-400" title="Request validation enabled">
                  <Shield size={12} className="mr-1" />
                  Validation
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
};

export default ApiNode;
