import React from 'react';
import { NodeProps } from 'reactflow';
import BaseNode from './BaseNode';
import { Code, Copy, Clock, Shield } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const ApiTriggerNode: React.FC<NodeProps> = ({ id, data, selected }) => {
  const { toast } = useToast();

  // Get the workflow ID from the URL or context (this is a simplified approach)
  const workflowId = window.location.pathname.split('/').pop();
  const triggerUrl = `${window.location.origin}/api/trigger/${workflowId}/${id}`;

  const copyTriggerUrl = () => {
    navigator.clipboard.writeText(triggerUrl);
    toast({
      title: "URL Copied",
      description: "API trigger URL copied to clipboard",
    });
  };

  const hasSecurityFeatures = data.authType !== 'none' ||
    (data.rateLimit?.enabled) ||
    (data.requestValidation?.enabled);

  return (
    <BaseNode
      id={id}
      data={data}
      icon={<Code size={16} />}
      color="#5C2D91"
      hasInput={false}
      hasOutput={true}
      selected={selected}
      executionStatus={data.executionStatus}
    >
      <div className="mt-2 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs font-mono overflow-hidden">
        <div className="flex items-center justify-between">
          <span>{data.allowedMethods?.join(', ') || 'ALL'}</span>
          <span className="text-green-600 dark:text-green-400">
            {data.authType && data.authType !== 'none' ? `🔒 ${data.authType === 'apiKey' ? 'API Key' : 'Bearer'}` : '🔓 Public'}
          </span>
        </div>
        <div className="mt-1 text-blue-600 dark:text-blue-400 truncate" title={triggerUrl}>
          /api/trigger/{workflowId}/{id}
        </div>
        <button
          onClick={copyTriggerUrl}
          className="mt-1 flex items-center text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200"
          title="Copy trigger URL"
        >
          <Copy size={12} className="mr-1" />
          Copy URL
        </button>

        {hasSecurityFeatures && (
          <div className="mt-2 pt-2 border-t border-neutral-200 dark:border-neutral-700">
            <div className="flex flex-wrap gap-2">
              {data.rateLimit?.enabled && (
                <div className="flex items-center text-amber-600 dark:text-amber-400" title={`Rate limit: ${data.rateLimit.requestsPerMinute} requests/minute`}>
                  <Clock size={12} className="mr-1" />
                  {data.rateLimit.requestsPerMinute}/min
                </div>
              )}

              {data.requestValidation?.enabled && (
                <div className="flex items-center text-amber-600 dark:text-amber-400" title="Request validation enabled">
                  <Shield size={12} className="mr-1" />
                  Validation
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
};

export default ApiTriggerNode;
