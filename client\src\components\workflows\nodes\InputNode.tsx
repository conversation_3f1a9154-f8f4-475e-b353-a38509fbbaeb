import React from 'react';
import { NodeProps } from 'reactflow';
import BaseNode from './BaseNode';
import { ArrowRight } from 'lucide-react';
import { InputSchema } from '@/types/workflow';

const InputNode: React.FC<NodeProps> = ({ id, data, selected, type, xPos, yPos, zIndex, isConnectable, targetPosition, sourcePosition, dragging }) => {
  // Handle both new and legacy schema formats
  const schema = data.schema;
  const isNewSchema = schema && typeof schema === 'object' && 'fields' in schema;

  const renderSchemaPreview = () => {
    if (isNewSchema) {
      const inputSchema = schema as InputSchema;
      return (
        <div className="space-y-1">
          <div className="font-medium text-xs">{inputSchema.title || 'Input Form'}</div>
          {inputSchema.fields.length > 0 ? (
            <div className="space-y-0.5">
              {inputSchema.fields.slice(0, 3).map((field) => (
                <div key={field.id} className="flex items-center justify-between text-xs">
                  <span className="text-neutral-600 dark:text-neutral-400">{field.label}</span>
                  <span className="text-neutral-500 text-xs">{field.type}</span>
                </div>
              ))}
              {inputSchema.fields.length > 3 && (
                <div className="text-xs text-neutral-500">+{inputSchema.fields.length - 3} more</div>
              )}
            </div>
          ) : (
            <div className="text-xs text-neutral-500">No fields defined</div>
          )}
        </div>
      );
    } else {
      // Legacy schema format
      return (
        <div className="font-mono text-xs">
          {schema ? JSON.stringify(schema, null, 2) : '{"query": "string"}'}
        </div>
      );
    }
  };

  return (
    <BaseNode
      id={id}
      data={data}
      icon={<ArrowRight size={16} />}
      color="#0078D4"
      hasInput={false}
      hasOutput={true}
      selected={selected}
      type={type}
      xPos={xPos}
      yPos={yPos}
      zIndex={zIndex}
      isConnectable={isConnectable}
      targetPosition={targetPosition}
      sourcePosition={sourcePosition}
      dragging={dragging}
      executionStatus={data.executionStatus}
    >
      <div className="mt-2 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs">
        {renderSchemaPreview()}
      </div>
    </BaseNode>
  );
};

export default InputNode;
