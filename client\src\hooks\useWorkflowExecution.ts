import { useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { WorkflowExecutionState, NodeExecutionState, WorkflowRun, NodeRun } from '@/types/workflow';

interface UseWorkflowExecutionProps {
  workflowId: number;
  enabled?: boolean;
}

export const useWorkflowExecution = ({ workflowId, enabled = true }: UseWorkflowExecutionProps) => {
  const [executionState, setExecutionState] = useState<WorkflowExecutionState>({
    status: 'idle',
    nodeStates: {}
  });

  // Get the latest workflow run
  const { data: workflowRuns, isLoading: isRunsLoading } = useQuery<WorkflowRun[]>({
    queryKey: [`/api/workflow-runs?workflowId=${workflowId}`],
    enabled,
    refetchInterval: (data) => {
      // Refetch every 2 seconds if there's an active run
      const latestRun = data?.[0];
      return latestRun && ['pending', 'running'].includes(latestRun.status) ? 2000 : false;
    },
  });

  const latestRun = workflowRuns?.[0];

  // Get node runs for the latest workflow run
  const { data: nodeRuns, isLoading: isNodeRunsLoading } = useQuery<NodeRun[]>({
    queryKey: [`/api/workflow-runs/${latestRun?.id}/node-runs`],
    enabled: !!latestRun?.id && enabled,
    refetchInterval: (data) => {
      // Refetch every 2 seconds if there are running nodes
      const hasRunningNodes = data?.some(nodeRun => ['pending', 'running'].includes(nodeRun.status));
      return hasRunningNodes ? 2000 : false;
    },
  });

  // Update execution state when data changes
  useEffect(() => {
    if (!latestRun) {
      setExecutionState({
        status: 'idle',
        nodeStates: {}
      });
      return;
    }

    const newNodeStates: Record<string, NodeExecutionState> = {};

    // Update node states from node runs
    if (nodeRuns) {
      nodeRuns.forEach(nodeRun => {
        newNodeStates[nodeRun.nodeId] = {
          nodeId: nodeRun.nodeId,
          status: nodeRun.status as NodeExecutionState['status'],
          startTime: nodeRun.startTime,
          endTime: nodeRun.endTime || undefined,
          executionDuration: nodeRun.executionDuration || undefined,
          input: nodeRun.input,
          output: nodeRun.output,
          error: nodeRun.error || undefined,
          nodeRunId: nodeRun.id
        };
      });
    }

    setExecutionState({
      workflowRunId: latestRun.id,
      status: latestRun.status as WorkflowExecutionState['status'],
      nodeStates: newNodeStates,
      startTime: latestRun.startTime,
      endTime: latestRun.endTime || undefined
    });
  }, [latestRun, nodeRuns]);

  // Get execution state for a specific node
  const getNodeExecutionState = useCallback((nodeId: string): NodeExecutionState => {
    return executionState.nodeStates[nodeId] || {
      nodeId,
      status: 'idle'
    };
  }, [executionState.nodeStates]);

  // Check if workflow is currently executing
  const isExecuting = executionState.status === 'running' || executionState.status === 'pending';

  // Check if any nodes are currently executing
  const hasExecutingNodes = Object.values(executionState.nodeStates).some(
    nodeState => nodeState.status === 'running' || nodeState.status === 'pending'
  );

  // Get execution summary
  const getExecutionSummary = useCallback(() => {
    const nodeStates = Object.values(executionState.nodeStates);
    return {
      total: nodeStates.length,
      completed: nodeStates.filter(state => state.status === 'completed').length,
      failed: nodeStates.filter(state => state.status === 'failed').length,
      running: nodeStates.filter(state => state.status === 'running').length,
      pending: nodeStates.filter(state => state.status === 'pending').length,
      idle: nodeStates.filter(state => state.status === 'idle').length
    };
  }, [executionState.nodeStates]);

  // Reset execution state (useful when starting a new run)
  const resetExecutionState = useCallback(() => {
    setExecutionState({
      status: 'idle',
      nodeStates: {}
    });
  }, []);

  // Start tracking a new workflow run
  const startTracking = useCallback((workflowRunId: number) => {
    setExecutionState(prev => ({
      ...prev,
      workflowRunId,
      status: 'pending'
    }));
  }, []);

  return {
    executionState,
    getNodeExecutionState,
    isExecuting,
    hasExecutingNodes,
    getExecutionSummary,
    resetExecutionState,
    startTracking,
    isLoading: isRunsLoading || isNodeRunsLoading,
    latestRun,
    nodeRuns
  };
};

export default useWorkflowExecution;
