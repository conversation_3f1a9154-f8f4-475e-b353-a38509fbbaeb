import React from 'react';
import { NodeProps } from 'reactflow';
import BaseNode from './BaseNode';
import { Puzzle } from 'lucide-react';

const CustomNode: React.FC<NodeProps> = ({ id, data, selected }) => {
  return (
    <BaseNode
      id={id}
      data={data}
      icon={<Puzzle size={16} />}
      color="#D83B01"
      hasInput={true}
      hasOutput={true}
      selected={selected}
      executionStatus={data.executionStatus}
    >
      <div className="mt-2 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs font-mono line-clamp-3">
        {data.code ? 'Custom logic defined' : 'No custom logic defined'}
      </div>

      <div className="mt-2 flex items-center text-xs text-neutral-600 dark:text-neutral-400">
        <span className="px-1.5 py-0.5 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 rounded">
          {data.inputs?.length || 0} inputs
        </span>
        <span className="ml-auto px-1.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded">
          {data.outputs?.length || 0} outputs
        </span>
      </div>
    </BaseNode>
  );
};

export default CustomNode;
